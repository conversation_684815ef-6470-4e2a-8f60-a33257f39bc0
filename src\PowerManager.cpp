#include "PowerManager.h"
#include "Config.h"
#include <esp_pm.h>
#include <esp_wifi.h>
#include <driver/adc.h>
#include <soc/rtc.h>

// RTC memory variables (preserved during deep sleep)
RTC_DATA_ATTR uint64_t g_totalUptimeSeconds = 0;
RTC_DATA_ATTR uint64_t g_lastSleepTimeMs = 0; // Keep in ms for millis() compatibility
RTC_DATA_ATTR uint64_t g_lastSleepDurationUs = 0; // Keep in us for ESP32 API compatibility

PowerManager::PowerManager() : initialized(false) {
    wakeupReason = esp_sleep_get_wakeup_cause();
}

void PowerManager::begin() {
    initialized = true;

    // Start with normal power mode
    setNormalPowerMode();

    // Initialize sensor power control
    initSensorPower();

    // Print wake-up reason for debugging
    printWakeupReason();

    // Update total uptime if waking from deep sleep
    if (!isFirstBoot()) {
        updateTotalUptime(g_lastSleepDurationUs);
        DEBUG_PRINTF("Updated total uptime after sleep: %llu seconds\n", g_totalUptimeSeconds);
    } else {
        // First boot - initialize RTC variables
        g_totalUptimeSeconds = 0;
        g_lastSleepTimeMs = 0;
        g_lastSleepDurationUs = 0;
        DEBUG_PRINTLN("First boot - initialized RTC variables");
    }

    // Configure wake-up source (timer only for now)
    esp_sleep_enable_timer_wakeup(calculateSleepDuration(300, 600));

#if USE_WIFI_POWER_SAVE && ENABLE_POWER_OPTIMIZATION
    // Configure WiFi power save mode
    esp_wifi_set_ps(WIFI_PS_MIN_MODEM);
    DEBUG_PRINTLN("WiFi power save mode enabled");
#endif

    DEBUG_PRINTLN("PowerManager initialized");
}

void PowerManager::enterDeepSleep(uint32_t sleepSeconds) {
    if (!initialized) {
        DEBUG_PRINTLN("PowerManager not initialized!");
        return;
    }

    // Calculate sleep duration
    uint64_t sleepDuration = sleepSeconds * SECOND_TO_MICROSECONDS;

    // Store current time and sleep duration for next wake-up
    g_lastSleepTimeMs = millis();
    g_lastSleepDurationUs = sleepDuration;

    // Update total uptime before sleep
    g_totalUptimeSeconds = getTotalUptime();

    DEBUG_PRINTF("Current uptime: %llu seconds, entering deep sleep for %u seconds...\n",
                 g_totalUptimeSeconds, sleepSeconds);
    Serial.flush(); // Ensure all serial output is sent

    // Prepare for sleep
    prepareForSleep();

    // Configure timer wake-up
    esp_sleep_enable_timer_wakeup(sleepDuration);

    // Enter deep sleep
    esp_deep_sleep_start();
}

esp_sleep_wakeup_cause_t PowerManager::getWakeupReason() {
    return wakeupReason;
}

void PowerManager::printWakeupReason() {
    switch (wakeupReason) {
        case ESP_SLEEP_WAKEUP_EXT0:
            DEBUG_PRINTLN("Wakeup caused by external signal using RTC_IO");
            break;
        case ESP_SLEEP_WAKEUP_EXT1:
            DEBUG_PRINTLN("Wakeup caused by external signal using RTC_CNTL");
            break;
        case ESP_SLEEP_WAKEUP_TIMER:
            DEBUG_PRINTLN("Wakeup caused by timer");
            break;
        case ESP_SLEEP_WAKEUP_TOUCHPAD:
            DEBUG_PRINTLN("Wakeup caused by touchpad");
            break;
        case ESP_SLEEP_WAKEUP_ULP:
            DEBUG_PRINTLN("Wakeup caused by ULP program");
            break;
        default:
            DEBUG_PRINTF("Wakeup was not caused by deep sleep: %d\n", wakeupReason);
            break;
    }
}

uint64_t PowerManager::calculateSleepDuration(uint32_t minSeconds, uint32_t maxSeconds) {
    if (maxSeconds <= minSeconds) {
        maxSeconds = minSeconds + 1;
    }

    // Generate random duration between min and max seconds
    uint32_t rangeMicroseconds = (maxSeconds - minSeconds) * 1000000;
    uint32_t randomOffset = esp_random() % rangeMicroseconds;
    uint64_t sleepDuration = (minSeconds * SECOND_TO_MICROSECONDS) + randomOffset;

    return sleepDuration;
}

bool PowerManager::isFirstBoot() {
    DEBUG_PRINTF("Wakeup reason: %d\n", wakeupReason);
    return (wakeupReason != ESP_SLEEP_WAKEUP_TIMER && 
            wakeupReason != ESP_SLEEP_WAKEUP_EXT0 && 
            wakeupReason != ESP_SLEEP_WAKEUP_EXT1 &&
            wakeupReason != ESP_SLEEP_WAKEUP_TOUCHPAD &&
            wakeupReason != ESP_SLEEP_WAKEUP_ULP);
}

void PowerManager::prepareForSleep() {
#if ENABLE_POWER_OPTIMIZATION
    DEBUG_PRINTLN("Preparing for deep sleep...");

    // Turn off sensor power
    sensorPowerOff();

    // Disable ADC
    adc_power_off();

    // Disable WiFi and Bluetooth
    esp_wifi_stop();
    esp_bt_controller_disable();

    DEBUG_PRINTLN("Peripherals powered down");
#endif

    // Flush any remaining serial output
    Serial.flush();

    // Small delay to ensure everything is settled
    delay(10);
}

uint64_t PowerManager::getTotalUptime() {
    // Current uptime is the total stored uptime plus the current session time in seconds
    return g_totalUptimeSeconds + (millis() / 1000);
}

void PowerManager::updateTotalUptime(uint64_t sleepDurationUs) {
    // Add the sleep duration (in seconds) to the total uptime
    g_totalUptimeSeconds += (sleepDurationUs / 1000000); // Convert microseconds to seconds
}

void PowerManager::setCpuFrequency(uint32_t freq_mhz) {
#if USE_DYNAMIC_CPU_FREQ
    if (freq_mhz != getCpuFrequencyMhz()) {
        DEBUG_PRINTF("Setting CPU frequency to %d MHz\n", freq_mhz);
        setCpuFrequencyMhz(freq_mhz);
    }
#endif
}

void PowerManager::setLowPowerMode() {
#if USE_DYNAMIC_CPU_FREQ
    setCpuFrequency(CPU_FREQ_LOW);
#endif
}

void PowerManager::setNormalPowerMode() {
#if USE_DYNAMIC_CPU_FREQ
    setCpuFrequency(CPU_FREQ_NORMAL);
#endif
}

void PowerManager::setHighPowerMode() {
#if USE_DYNAMIC_CPU_FREQ
    setCpuFrequency(CPU_FREQ_HIGH);
#endif
}

void PowerManager::initSensorPower() {
#if SENSOR_POWER_PIN >= 0
    pinMode(SENSOR_POWER_PIN, OUTPUT);
    sensorPowerOff(); // Start with sensor powered off
    DEBUG_PRINTLN("Sensor power control initialized");
#endif
}

void PowerManager::sensorPowerOn() {
#if SENSOR_POWER_PIN >= 0
    digitalWrite(SENSOR_POWER_PIN, HIGH);
    DEBUG_PRINTLN("Sensor power ON");
#endif
}

void PowerManager::sensorPowerOff() {
#if SENSOR_POWER_PIN >= 0
    digitalWrite(SENSOR_POWER_PIN, LOW);
    DEBUG_PRINTLN("Sensor power OFF");
#endif
}

void PowerManager::enterLightSleep(uint32_t ms) {
#if ENABLE_POWER_OPTIMIZATION
    DEBUG_PRINTF("Entering light sleep for %d ms\n", ms);

    // Configure timer wake-up
    esp_sleep_enable_timer_wakeup(ms * 1000); // Convert to microseconds

    // Enter light sleep
    esp_light_sleep_start();

    DEBUG_PRINTLN("Woke up from light sleep");
#else
    // Fallback to delay if power optimization is disabled
    delay(ms);
#endif
}
