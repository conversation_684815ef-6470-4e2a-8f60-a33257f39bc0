#ifndef CONNECTIVITY_MANAGER_H
#define CONNECTIVITY_MANAGER_H

#include <Arduino.h>
#include <WiFi.h>
#include <WiFiClientSecure.h>
#include <PubSubClient.h>
#include <ArduinoJson.h>
#include <WiFiProv.h>
#include <Preferences.h>
#include <esp_wifi.h>
#include <time.h>
#include <sys/time.h>
#include "SensorDataManager.h"

// Forward declaration
class PowerManager;

class ConnectivityManager {
public:
    ConnectivityManager(const char *mqttServer, uint16_t mqttPort);
    ~ConnectivityManager();

    // Initialize WiFi and MQTT (BluFi mode)
    bool begin();

    // Start BluFi provisioning process
    bool startBluFiProvisioning(uint32_t timeoutMs = 180000); // 3 minutes default

    // Connect to WiFi using stored or provided credentials
    bool connectWiFi(uint32_t timeoutMs = 30000);

    // Connect to MQTT broker
    bool connectMQTT(uint32_t timeoutMs = 10000);

    // Disconnect and cleanup
    void disconnect();

    // Check if connected to both WiFi and MQTT
    bool isConnected();

    // Check if WiFi credentials are stored
    bool hasStoredCredentials();

    // Clear stored WiFi credentials
    void clearStoredCredentials();

    // Get WiFi connection failure count
    uint8_t getWiFiFailureCount();

    // Reset WiFi failure count
    void resetWiFiFailureCount();

    // Increment WiFi failure count
    void incrementWiFiFailureCount();

    // Publish sensor data with next detection and daily report times
    bool publishSensorData(const SensorReading& reading, bool isPeriodicReport = false, time_t nextPeriodicReportTime = 0);

    // Publish sensor error/failure status
    bool publishSensorError(const char* errorMessage, time_t nextPeriodicReportTime = 0);

    // Publish status/heartbeat
    bool publishStatus(uint32_t bootCount, const char* status = "online");

    // Handle MQTT loop (call regularly if staying connected)
    void loop();

    // Get connection status string
    String getConnectionStatus();

    // Set power manager reference for next detection time calculation
    void setPowerManager(PowerManager* pm);

    // Sync time with NTP server (Beijing timezone)
    bool syncTimeWithNTP();

    // Get current Beijing time as Unix timestamp
    time_t getBeijingTimestamp();

    // Get formatted Beijing time string
    String getBeijingTimeString();

    // Optimize WiFi power based on signal strength
    void optimizeWiFiPower();

    // Set WiFi transmission power
    void setWiFiTxPower(int8_t power);

private:
    WiFiClient wifiClient;
    WiFiClientSecure wifiClientSecure;
    PubSubClient mqttClient;
    Preferences preferences;

    // Connection parameters
    String mqttServer;
    uint16_t mqttPort;
    String mqttUser;
    String mqttPassword;

    // Device identification
    String deviceId;
    String clientId;

    // MQTT topics
    String topicSensorData;
    String topicStatus;
    String topicHeartbeat;

    bool initialized;
    bool wifiConnected;
    bool mqttConnected;
    bool provisioningActive;

    // WiFi failure tracking
    uint8_t wifiFailureCount;
    static const uint8_t MAX_WIFI_FAILURES = 3;

    // Power manager reference for next detection time calculation
    PowerManager* powerManager;

    // Connection timeouts
    static const uint32_t WIFI_RETRY_INTERVAL = 500;
    static const uint32_t MQTT_RETRY_INTERVAL = 1000;
    static const uint32_t BLUFI_TIMEOUT_MS = 180000; // 3 minutes

    // NTP configuration
    static const char* NTP_SERVER1;
    static const char* NTP_SERVER2;
    static const char* NTP_SERVER3;
    static const long GMT_OFFSET_SEC = 8 * 3600; // Beijing timezone (UTC+8)
    static const int DAYLIGHT_OFFSET_SEC = 0;    // No daylight saving in China

    // BluFi provisioning parameters
    static const char* BLUFI_POP;
    static const char* BLUFI_SERVICE_NAME;

    // Preferences keys
    static const char* PREF_NAMESPACE;
    static const char* PREF_WIFI_FAILURE_COUNT;

    // Generate device ID based on MAC address
    void generateDeviceId();

    // Setup MQTT topics
    void setupTopics();

    // MQTT callback (for future use if needed)
    static void mqttCallback(char* topic, byte* payload, unsigned int length);

    // Create JSON payload for sensor data
    String createSensorDataPayload(const SensorReading& reading, bool isPeriodicReport, time_t nextPeriodicReportTime = 0);

    // Create JSON payload for status
    String createStatusPayload(uint32_t bootCount, const char* status);

    // WiFi event handlers
    void onWiFiConnected();
    void onWiFiDisconnected();

    // BluFi event handler
    static void bluFiEventHandler(arduino_event_t *sys_event);

    // Static instance pointer for event handler
    static ConnectivityManager* instance;

    // Load WiFi failure count from preferences
    void loadWiFiFailureCount();

    // Save WiFi failure count to preferences
    void saveWiFiFailureCount();
};

#endif // CONNECTIVITY_MANAGER_H
